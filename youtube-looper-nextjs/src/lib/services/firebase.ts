// Firebase Firestore service for queue management

import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'
import { getFirebaseDb } from '@/lib/firebase/config'
import { PersonalQueue, PublicQueue, QueueState, QueueMetadata } from '@/lib/types/queue'
import { prepareForFirebase, validateFirebaseData, timestampToMillis } from '@/lib/utils/firebase'
import { VideoMetadata } from '@/lib/types/video'

export class FirebaseService {
  private getDb() {
    const db = getFirebaseDb()
    if (!db) {
      console.warn('Firebase not initialized. Queue persistence will not work.')
    }
    return db
  }

  // Personal Queues
  async savePersonalQueue(userId: string, queueData: QueueState, metadata: Partial<QueueMetadata>): Promise<string | null> {
    const db = this.getDb()
    if (!db) return null

    try {
      const queueRef = collection(db, 'personal_queues')
      const queueDoc = {
        userId,
        queueData,
        metadata: {
          ...metadata,
          videoCount: queueData.items.length,
          totalDuration: queueData.items.reduce((total, item) => total + (item.duration || 0), 0),
          firstVideoThumbnail: queueData.items[0]?.thumbnail || '',
          createdAt: Date.now(),
          lastModified: Date.now(),
          viewCount: 0,
          isPublic: false,
        },
        isPersonal: true,
        isPublic: false,
        createdAt: serverTimestamp(),
        lastModified: serverTimestamp(),
      }

      // Clean the document data to remove undefined values
      const cleanedQueueDoc = prepareForFirebase(queueDoc)

      // Validate the data before saving
      const validation = validateFirebaseData(cleanedQueueDoc, 'queueDoc')
      if (!validation.isValid) {
        console.error('❌ Invalid data for Firebase:', validation.errors)
        throw new Error(`Invalid data: ${validation.errors.join(', ')}`)
      }

      const docRef = await addDoc(queueRef, cleanedQueueDoc)
      console.log('✅ Personal queue saved:', docRef.id)
      return docRef.id
    } catch (error) {
      console.error('❌ Error saving personal queue:', error)
      return null
    }
  }

  async getPersonalQueue(queueId: string): Promise<PersonalQueue | null> {
    const db = this.getDb()
    if (!db) return null

    try {
      const queueRef = doc(db, 'personal_queues', queueId)
      const queueSnap = await getDoc(queueRef)

      if (!queueSnap.exists()) {
        console.warn('Personal queue not found:', queueId)
        return null
      }

      const data = queueSnap.data()
      const queue: PersonalQueue = {
        id: queueSnap.id,
        ...data,
        createdAt: timestampToMillis(data.createdAt),
        lastModified: timestampToMillis(data.lastModified),
      } as PersonalQueue

      console.log('✅ Personal queue loaded:', queueId)
      return queue
    } catch (error) {
      console.error('❌ Error loading personal queue:', error)
      return null
    }
  }

  async getPersonalQueues(userId: string): Promise<PersonalQueue[]> {
    const db = this.getDb()
    if (!db) return []

    try {
      const q = query(
        collection(db, 'personal_queues'),
        where('userId', '==', userId),
        orderBy('lastModified', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const queues: PersonalQueue[] = []
      
      querySnapshot.forEach((doc) => {
        const data = doc.data()
        queues.push({
          id: doc.id,
          ...data,
          createdAt: timestampToMillis(data.createdAt),
          lastModified: timestampToMillis(data.lastModified),
        } as PersonalQueue)
      })
      
      return queues
    } catch (error) {
      console.error('❌ Error fetching personal queues:', error)
      return []
    }
  }

  async updatePersonalQueue(queueId: string, updates: Partial<PersonalQueue>): Promise<boolean> {
    const db = this.getDb()
    if (!db) return false

    try {
      const queueRef = doc(db, 'personal_queues', queueId)
      const updateData = {
        ...updates,
        lastModified: serverTimestamp(),
      }

      // Clean the update data to remove undefined values
      const cleanedUpdateData = prepareForFirebase(updateData)

      await updateDoc(queueRef, cleanedUpdateData)
      console.log('✅ Personal queue updated:', queueId)
      return true
    } catch (error) {
      console.error('❌ Error updating personal queue:', error)
      return false
    }
  }

  async updatePersonalQueueMetadata(queueId: string, metadata: Partial<QueueMetadata>): Promise<boolean> {
    const db = this.getDb()
    if (!db) return false

    try {
      const queueRef = doc(db, 'personal_queues', queueId)
      const updateData = {
        metadata: {
          ...metadata,
          lastModified: Date.now(),
        },
        lastModified: serverTimestamp(),
      }

      const cleanedUpdateData = prepareForFirebase(updateData)
      await updateDoc(queueRef, cleanedUpdateData)
      console.log('✅ Personal queue metadata updated:', queueId)
      return true
    } catch (error) {
      console.error('❌ Error updating personal queue metadata:', error)
      return false
    }
  }

  async updatePersonalQueueContent(queueId: string, queueData: QueueState): Promise<boolean> {
    const db = this.getDb()
    if (!db) return false

    try {
      const queueRef = doc(db, 'personal_queues', queueId)

      // Calculate updated metadata
      const videoCount = queueData.items.length
      const totalDuration = queueData.items.reduce((total, item) => total + (item.duration || 0), 0)
      const firstVideoThumbnail = queueData.items[0]?.thumbnail || ''

      const updateData = {
        queueData,
        'metadata.videoCount': videoCount,
        'metadata.totalDuration': totalDuration,
        'metadata.firstVideoThumbnail': firstVideoThumbnail,
        'metadata.lastModified': Date.now(),
        lastModified: serverTimestamp(),
      }

      const cleanedUpdateData = prepareForFirebase(updateData)
      await updateDoc(queueRef, cleanedUpdateData)
      console.log('✅ Personal queue content updated:', queueId)
      return true
    } catch (error) {
      console.error('❌ Error updating personal queue content:', error)
      return false
    }
  }

  async duplicatePersonalQueue(queueId: string, userId: string, newTitle?: string): Promise<string | null> {
    const db = this.getDb()
    if (!db) return null

    try {
      // Get the original queue
      const originalQueueRef = doc(db, 'personal_queues', queueId)
      const originalQueueSnap = await getDoc(originalQueueRef)

      if (!originalQueueSnap.exists()) {
        throw new Error('Original queue not found')
      }

      const originalQueue = originalQueueSnap.data() as PersonalQueue

      // Create new queue data
      const duplicatedQueueData = {
        userId,
        queueData: originalQueue.queueData,
        metadata: {
          ...originalQueue.metadata,
          title: newTitle || `${originalQueue.metadata.title} (Copy)`,
          createdAt: Date.now(),
          lastModified: Date.now(),
          viewCount: 0,
          isPublic: false,
        },
        isPersonal: true,
        isPublic: false,
        createdAt: serverTimestamp(),
        lastModified: serverTimestamp(),
      }

      const cleanedData = prepareForFirebase(duplicatedQueueData)
      const newQueueRef = await addDoc(collection(db, 'personal_queues'), cleanedData)

      console.log('✅ Personal queue duplicated:', newQueueRef.id)
      return newQueueRef.id
    } catch (error) {
      console.error('❌ Error duplicating personal queue:', error)
      return null
    }
  }

  async deletePersonalQueue(queueId: string): Promise<boolean> {
    const db = this.getDb()
    if (!db) return false

    try {
      await deleteDoc(doc(db, 'personal_queues', queueId))
      console.log('✅ Personal queue deleted:', queueId)
      return true
    } catch (error) {
      console.error('❌ Error deleting personal queue:', error)
      return false
    }
  }

  async updatePersonalQueuePrivacy(queueId: string, isPublic: boolean, userId: string): Promise<boolean> {
    const db = this.getDb()
    if (!db) return false

    try {
      const personalQueueRef = doc(db, 'personal_queues', queueId)
      const personalQueueSnap = await getDoc(personalQueueRef)

      if (!personalQueueSnap.exists()) {
        throw new Error('Personal queue not found')
      }

      const personalQueue = personalQueueSnap.data() as PersonalQueue

      if (isPublic) {
        // Making queue public - add to public queues collection
        console.log('📤 Making queue public:', queueId)

        const publicQueueRef = collection(db, 'queues')
        const publicQueueDoc = {
          queueData: personalQueue.queueData,
          metadata: {
            ...personalQueue.metadata,
            isPublic: true,
          },
          isPublic: true,
          createdAt: serverTimestamp(),
          lastModified: serverTimestamp(),
          createdBy: userId,
        }

        const docRef = await addDoc(publicQueueRef, publicQueueDoc)
        console.log('✅ Public queue created with ID:', docRef.id)

        // Update personal queue to mark as public
        await updateDoc(personalQueueRef, {
          isPublic: true,
          publicQueueId: docRef.id,
          lastModified: serverTimestamp(),
        })
      } else {
        // Making queue private - remove from public queues collection
        console.log('🔒 Making queue private:', queueId)

        // If there's a linked public queue, delete it
        if (personalQueue.publicQueueId) {
          try {
            await deleteDoc(doc(db, 'queues', personalQueue.publicQueueId))
            console.log('✅ Removed from public queues collection')
          } catch (error) {
            console.warn('Could not remove from public collection:', error)
          }
        }

        // Update personal queue to mark as private
        await updateDoc(personalQueueRef, {
          isPublic: false,
          publicQueueId: null,
          lastModified: serverTimestamp(),
        })
      }

      console.log(`✅ Queue privacy updated: ${isPublic ? 'Public' : 'Private'}`)
      return true
    } catch (error) {
      console.error('❌ Error updating queue privacy:', error)
      return false
    }
  }

  // Public Queues
  async shareQueue(queueId: string, userId: string): Promise<string | null> {
    const db = this.getDb()
    if (!db) return null

    try {
      // Get the personal queue
      const personalQueueRef = doc(db, 'personal_queues', queueId)
      const personalQueueSnap = await getDoc(personalQueueRef)

      if (!personalQueueSnap.exists()) {
        throw new Error('Personal queue not found')
      }

      const personalQueue = personalQueueSnap.data() as PersonalQueue

      // Create public queue with proper structure
      const publicQueueRef = collection(db, 'queues')
      const publicQueueDoc = {
        queueData: personalQueue.queueData,
        metadata: {
          ...personalQueue.metadata,
          isPublic: true,
        },
        isPublic: true, // Add top-level isPublic field for easier querying
        createdAt: serverTimestamp(),
        lastModified: serverTimestamp(),
        createdBy: userId,
      }

      console.log('💾 Creating public queue with data:', publicQueueDoc)
      const docRef = await addDoc(publicQueueRef, publicQueueDoc)
      console.log('✅ Public queue created with ID:', docRef.id)

      // Update personal queue to mark as public
      await updateDoc(personalQueueRef, {
        isPublic: true,
        publicQueueId: docRef.id,
        lastModified: serverTimestamp(),
      })

      // Verify the document was created
      const verifyDoc = await getDoc(doc(db, 'queues', docRef.id))
      if (verifyDoc.exists()) {
        console.log('✅ Public queue verified in database:', verifyDoc.data())
      } else {
        console.error('❌ Public queue not found after creation')
      }

      console.log('✅ Queue shared publicly:', docRef.id)
      return docRef.id
    } catch (error) {
      console.error('❌ Error sharing queue:', error)
      return null
    }
  }

  async getPublicQueues(limitCount: number = 20): Promise<PublicQueue[]> {
    const db = this.getDb()
    if (!db) return []

    try {
      console.log('🔍 Fetching public queues from Firestore...')

      // First, try to get all documents to see what's in the collection
      const allDocsQuery = query(collection(db, 'queues'), limit(50))
      const allDocsSnapshot = await getDocs(allDocsQuery)

      console.log(`📊 Found ${allDocsSnapshot.size} total documents in 'queues' collection`)

      if (allDocsSnapshot.size === 0) {
        console.log('📭 No documents found in queues collection')
        return []
      }

      // Log the first few documents to understand the structure
      allDocsSnapshot.docs.slice(0, 3).forEach((doc, index) => {
        const data = doc.data()
        console.log(`📄 Document ${index + 1}:`, {
          id: doc.id,
          isPublic: data.isPublic,
          metadataIsPublic: data.metadata?.isPublic,
          hasQueueData: !!data.queueData,
          hasMetadata: !!data.metadata,
          createdBy: data.createdBy,
          structure: Object.keys(data)
        })
        console.log(`📄 Full data for Document ${index + 1}:`, data)
      })

      // The indexed query isn't working, so let's use the manual filtering approach
      // Get all documents and filter manually
      const q = query(
        collection(db, 'queues'),
        limit(limitCount * 3) // Get more to filter manually
      )
      const allDocs = await getDocs(q)
      console.log(`📋 Retrieved ${allDocs.size} documents for manual filtering`)

      // Filter manually for public queues
      const filteredDocs: any[] = []
      allDocs.forEach((doc) => {
        const data = doc.data()
        console.log(`🔍 Checking document ${doc.id}:`, {
          isPublic: data.isPublic,
          metadataIsPublic: data.metadata?.isPublic,
          startsWithPersonal: doc.id.startsWith('personal_')
        })

        // Include documents that are public queues:
        // 1. Documents with queue_ prefix (original public queues)
        // 2. Documents explicitly marked as public
        if (doc.id.startsWith('queue_') ||
            data.isPublic === true ||
            data.metadata?.isPublic === true) {
          console.log(`✅ Including document ${doc.id} as public queue`)
          filteredDocs.push(doc)
        } else {
          console.log(`❌ Excluding document ${doc.id} - not a public queue`)
        }
      })

      console.log(`📋 Filtered to ${filteredDocs.length} public queues`)

      // Create a mock QuerySnapshot-like object
      const querySnapshot = {
        size: filteredDocs.length,
        forEach: (callback: any) => filteredDocs.forEach(callback)
      } as any

      const queues: PublicQueue[] = []

      querySnapshot.forEach((doc: any) => {
        const data = doc.data()
        console.log('🔍 Processing document:', doc.id, data)

        queues.push({
          id: doc.id,
          ...data,
          createdAt: timestampToMillis(data.createdAt),
          lastModified: timestampToMillis(data.lastModified),
        } as PublicQueue)
      })

      // Sort manually if we used the simple query
      if (queues.length > 0) {
        queues.sort((a, b) => b.lastModified - a.lastModified)
      }

      console.log(`✅ Returning ${queues.length} public queues`)
      return queues
    } catch (error) {
      console.error('❌ Error fetching public queues:', error)
      console.error('Error details:', error)
      return []
    }
  }

  async getPublicQueue(queueId: string): Promise<PublicQueue | null> {
    const db = this.getDb()
    if (!db) return null

    try {
      const queueRef = doc(db, 'queues', queueId)
      const queueSnap = await getDoc(queueRef)
      
      if (!queueSnap.exists()) {
        return null
      }

      const data = queueSnap.data()
      return {
        id: queueSnap.id,
        ...data,
        createdAt: data.createdAt?.toMillis() || Date.now(),
        lastModified: data.lastModified?.toMillis() || Date.now(),
      } as PublicQueue
    } catch (error) {
      console.error('❌ Error fetching public queue:', error)
      return null
    }
  }

  async searchPublicQueues(searchTerm: string, limitCount: number = 20): Promise<PublicQueue[]> {
    const db = this.getDb()
    if (!db) return []

    try {
      console.log('🔍 Searching public queues for:', searchTerm)

      // Note: Firestore doesn't support full-text search natively
      // Get all documents and filter manually (same as getPublicQueues)
      const q = query(
        collection(db, 'queues'),
        limit(limitCount * 5) // Get more to filter by search term
      )

      const querySnapshot = await getDocs(q)
      const queues: PublicQueue[] = []

      querySnapshot.forEach((doc) => {
        const data = doc.data()

        // Include documents that are marked as public OR have queue_ prefix (legacy public queues)
        if (data.isPublic === true ||
            data.metadata?.isPublic === true ||
            doc.id.startsWith('queue_')) {

          const queue = {
            id: doc.id,
            ...data,
            createdAt: timestampToMillis(data.createdAt),
            lastModified: timestampToMillis(data.lastModified),
          } as PublicQueue

          // Filter by search term
          if (
            queue.metadata?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            queue.metadata?.description?.toLowerCase().includes(searchTerm.toLowerCase())
          ) {
            queues.push(queue)
          }
        }
      })

      console.log(`🔍 Found ${queues.length} matching public queues`)
      return queues.slice(0, limitCount)
    } catch (error) {
      console.error('❌ Error searching public queues:', error)
      return []
    }
  }

  // Real-time listeners
  subscribeToPersonalQueues(userId: string, callback: (queues: PersonalQueue[]) => void): () => void {
    const db = this.getDb()
    if (!db) return () => {}

    const q = query(
      collection(db, 'personal_queues'),
      where('userId', '==', userId),
      orderBy('lastModified', 'desc')
    )

    return onSnapshot(q, (querySnapshot) => {
      const queues: PersonalQueue[] = []
      querySnapshot.forEach((doc) => {
        const data = doc.data()
        queues.push({
          id: doc.id,
          ...data,
          createdAt: timestampToMillis(data.createdAt),
          lastModified: timestampToMillis(data.lastModified),
        } as PersonalQueue)
      })
      callback(queues)
    })
  }

  subscribeToPublicQueues(callback: (queues: PublicQueue[]) => void, limitCount: number = 20): () => void {
    const db = this.getDb()
    if (!db) return () => {}

    const q = query(
      collection(db, 'queues'),
      where('isPublic', '==', true),
      orderBy('lastModified', 'desc'),
      limit(limitCount)
    )

    return onSnapshot(q, (querySnapshot) => {
      const queues: PublicQueue[] = []
      querySnapshot.forEach((doc) => {
        const data = doc.data()

        // Skip personal queues that ended up in wrong collection
        if (doc.id.startsWith('personal_')) {
          return
        }

        queues.push({
          id: doc.id,
          ...data,
          createdAt: timestampToMillis(data.createdAt),
          lastModified: timestampToMillis(data.lastModified),
        } as PublicQueue)
      })
      callback(queues)
    })
  }

  // Temporary method to create a test public queue
  async createTestPublicQueue(): Promise<string | null> {
    const db = this.getDb()
    if (!db) return null

    try {
      const testQueueData = {
        items: [
          {
            id: 'dQw4w9WgXcQ',
            title: 'Rick Astley - Never Gonna Give You Up',
            thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
            duration: 213,
            channel: 'Rick Astley',
            url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            addedAt: Date.now(),
            queueIndex: 0
          }
        ],
        currentIndex: 0,
        isPlaying: false,
        isLooping: true,
        shuffle: false,
        volume: 1,
        timestamp: Date.now()
      }

      const testMetadata = {
        id: 'test-public-queue',
        title: 'Test Public Queue',
        description: 'A test queue to verify public queue functionality',
        videoCount: 1,
        totalDuration: 213,
        firstVideoThumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
        createdAt: Date.now(),
        lastModified: Date.now(),
        viewCount: 0,
        isPublic: true
      }

      const publicQueueDoc = {
        queueData: testQueueData,
        metadata: testMetadata,
        isPublic: true,
        createdAt: serverTimestamp(),
        lastModified: serverTimestamp(),
        createdBy: 'test-user'
      }

      console.log('🧪 Creating test public queue:', publicQueueDoc)
      const docRef = await addDoc(collection(db, 'queues'), publicQueueDoc)
      console.log('✅ Test public queue created with ID:', docRef.id)
      return docRef.id
    } catch (error) {
      console.error('❌ Error creating test public queue:', error)
      return null
    }
  }
}

// Export singleton instance
export const firebaseService = new FirebaseService()

// Expose for testing in browser console (with delay to ensure initialization)
if (typeof window !== 'undefined') {
  setTimeout(() => {
    (window as any).firebaseService = firebaseService;
    (window as any).createTestPublicQueue = () => firebaseService.createTestPublicQueue();
    console.log('🧪 Test functions available: firebaseService, createTestPublicQueue()');
  }, 1000);
}
