'use client'

import { useState } from 'react'
import { SearchInput } from './SearchInput'
import { SearchResults } from './SearchResults'
import { youtubeService } from '@/lib/services/youtube'
import { VideoSearchResult } from '@/lib/types/video'
import { useAuth } from '@/hooks/useAuth'

import { useDraftQueue } from '@/hooks/useDraftQueue'
import { DraftQueue } from '@/components/draft-queue/DraftQueue'
import { QueueCreationForm } from '@/components/draft-queue/QueueCreationForm'

export function SearchView() {
  const { user, isAuthenticated } = useAuth()
  const {
    draftItems,
    draftCount,
    isCreationMode,
    isEditMode,
    enterCreationMode,
    exitCreationMode
  } = useDraftQueue()

  // Search state
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<VideoSearchResult[]>([])
  const [error, setError] = useState<string | null>(null)

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([])
      setError(null)
      return
    }

    setIsSearching(true)
    setSearchQuery(query)
    setError(null)

    try {
      console.log('🔍 Searching YouTube for:', query)
      const results = await youtubeService.searchVideos(query, 20)
      setSearchResults(results)
      console.log('✅ Found', results.length, 'videos')
    } catch (error: any) {
      console.error('❌ Search error:', error)
      setError(error.message || 'Failed to search videos')
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  const handleEnterCreationMode = () => {
    enterCreationMode()
  }



  return (
    <div className="space-y-6">
      {/* Queue Creation Mode Header */}
      {isCreationMode && (
        <div className="glassmorphism rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                +
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Creating New Queue</h3>
                <p className="text-dark-300">Add videos to create your new queue</p>
              </div>
            </div>
            <button
              onClick={exitCreationMode}
              className="p-2 text-dark-300 hover:text-white transition-colors"
              title="Cancel"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* New Queue Button (when not in creation or edit mode) */}
      {!isCreationMode && !isEditMode && (
        <div className="glassmorphism rounded-2xl p-8 text-center">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">Create Your Music Queue</h2>
            <p className="text-dark-300">Build custom playlists with your favorite YouTube videos</p>
          </div>
          <button
            onClick={handleEnterCreationMode}
            className="btn-primary text-lg px-8 py-4"
            title="Create New Queue"
          >
            Create New Queue
          </button>
        </div>
      )}

      {/* Search Section (show when in creation or edit mode) */}
      {(isCreationMode || isEditMode) && (
        <>
          {/* Search Header */}
          <div className="glassmorphism rounded-2xl p-6">
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-white mb-2">
                {isEditMode ? 'Edit Queue - Search Videos' : 'Search YouTube Videos'}
              </h1>
              <p className="text-dark-300">
                {isEditMode ? 'Find and add more videos to your queue' : 'Find and add videos to your queue'}
              </p>
            </div>

            <SearchInput
              onSearch={handleSearch}
              isLoading={isSearching}
              placeholder="Search for videos, artists, or songs..."
            />
          </div>

          {/* Error Message */}
          {error && (
            <div className="glassmorphism rounded-2xl p-6">
              <div className="flex items-center space-x-3 text-red-400">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                </svg>
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Search Results */}
          <SearchResults
            results={searchResults}
            isLoading={isSearching}
            query={searchQuery}
            error={error}
          />
        </>
      )}

      {/* Draft Queue Display (show when in creation or edit mode) - MOVED TO BOTTOM */}
      {(isCreationMode || isEditMode) && <DraftQueue />}

      {/* Queue Creation/Edit Form (show when in creation or edit mode) - MOVED TO BOTTOM */}
      {(isCreationMode || isEditMode) && (
        <QueueCreationForm />
      )}
    </div>
  )
}
