'use client'

import { useState } from 'react'
import { VideoMetadata } from '@/lib/types/video'
import { formatDuration } from '@/lib/utils/format'

interface VideoSearchInputProps {
  onVideoSelect: (video: VideoMetadata) => void
  placeholder?: string
  className?: string
}

export function VideoSearchInput({ onVideoSelect, placeholder = "Search for videos...", className = "" }: VideoSearchInputProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<VideoMetadata[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [showResults, setShowResults] = useState(false)

  const searchVideos = async () => {
    if (!searchQuery.trim() || isSearching) return

    setIsSearching(true)
    setShowResults(true)

    try {
      // This is a placeholder for YouTube API integration
      // In a real implementation, you would call the YouTube API here
      console.log('Searching for:', searchQuery)
      
      // Mock search results for demonstration
      const mockResults: VideoMetadata[] = [
        {
          id: `mock-${Date.now()}-1`,
          title: `Search result for "${searchQuery}" - Video 1`,
          channelTitle: 'Mock Channel 1',
          thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
          duration: 213,
          publishedAt: new Date().toISOString(),
          description: 'Mock video description',
          viewCount: 1000000,
          likeCount: 50000,
          tags: ['music', 'entertainment'],
        },
        {
          id: `mock-${Date.now()}-2`,
          title: `Search result for "${searchQuery}" - Video 2`,
          channelTitle: 'Mock Channel 2',
          thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
          duration: 180,
          publishedAt: new Date().toISOString(),
          description: 'Another mock video description',
          viewCount: 500000,
          likeCount: 25000,
          tags: ['tutorial', 'education'],
        },
      ]

      setSearchResults(mockResults)
    } catch (error) {
      console.error('Search error:', error)
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  const handleVideoSelect = (video: VideoMetadata) => {
    onVideoSelect(video)
    setSearchQuery('')
    setSearchResults([])
    setShowResults(false)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      searchVideos()
    } else if (e.key === 'Escape') {
      setShowResults(false)
    }
  }

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="flex gap-2">
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={handleKeyPress}
          onFocus={() => searchResults.length > 0 && setShowResults(true)}
          className="flex-1 px-4 py-2 bg-dark-800 border border-white/10 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500"
          placeholder={placeholder}
        />
        <button
          onClick={searchVideos}
          disabled={isSearching || !searchQuery.trim()}
          className="btn-primary px-6 py-2 disabled:opacity-50"
        >
          {isSearching ? 'Searching...' : 'Search'}
        </button>
      </div>

      {/* Search Results Dropdown */}
      {showResults && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-dark-800 border border-white/10 rounded-lg shadow-xl z-50 max-h-80 overflow-y-auto">
          {isSearching ? (
            <div className="p-4 text-center text-dark-400">
              <div className="loading-spinner w-6 h-6 mx-auto mb-2"></div>
              Searching...
            </div>
          ) : searchResults.length > 0 ? (
            <div className="p-2">
              {searchResults.map((video) => (
                <button
                  key={video.id}
                  onClick={() => handleVideoSelect(video)}
                  className="w-full flex items-center gap-3 p-3 hover:bg-dark-700 rounded-lg transition-colors duration-200 text-left"
                >
                  <div className="flex-shrink-0 w-16 h-12 bg-dark-700 rounded overflow-hidden">
                    {video.thumbnail && (
                      <img
                        src={video.thumbnail}
                        alt={video.title}
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-white font-medium truncate">{video.title}</h4>
                    <p className="text-sm text-dark-400 truncate">{video.channelTitle}</p>
                    <p className="text-xs text-dark-500">{formatDuration(video.duration || 0)}</p>
                  </div>
                  <div className="flex-shrink-0">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-primary-400">
                      <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                  </div>
                </button>
              ))}
            </div>
          ) : searchQuery.trim() && !isSearching ? (
            <div className="p-4 text-center text-dark-400">
              No videos found for "{searchQuery}"
            </div>
          ) : null}

          {/* Close button */}
          <div className="p-2 border-t border-white/10">
            <button
              onClick={() => setShowResults(false)}
              className="w-full text-sm text-dark-400 hover:text-white py-2 transition-colors duration-200"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Backdrop to close dropdown */}
      {showResults && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowResults(false)}
        />
      )}
    </div>
  )
}
