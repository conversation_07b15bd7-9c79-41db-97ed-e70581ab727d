'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useQueue } from '@/hooks/useQueue'
import { firebaseService } from '@/lib/services/firebase'
import { PersonalQueue } from '@/lib/types/queue'
import { PersonalQueueCard } from './PersonalQueueCard'
import { QueueBulkActions } from './QueueBulkActions'
import { QueueStats } from './QueueStats'

export function PersonalQueuesView() {
  const { user, isAuthenticated } = useAuth()
  const { loadQueue } = useQueue()
  const [personalQueues, setPersonalQueues] = useState<PersonalQueue[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedQueues, setSelectedQueues] = useState<PersonalQueue[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Load personal queues
  useEffect(() => {
    if (isAuthenticated && user) {
      loadPersonalQueues()
    } else {
      setIsLoading(false)
    }
  }, [isAuthenticated, user])

  const loadPersonalQueues = async () => {
    if (!user) return

    try {
      setIsLoading(true)
      const queues = await firebaseService.getPersonalQueues(user.uid)
      setPersonalQueues(queues)
    } catch (error) {
      console.error('Failed to load personal queues:', error)
    } finally {
      setIsLoading(false)
    }
  }



  const handleLoadQueue = (queue: PersonalQueue) => {
    loadQueue(queue.queueData)
    console.log('✅ Queue loaded:', queue.metadata.title)
  }

  const handleDeleteQueue = async (queueId: string) => {
    try {
      const success = await firebaseService.deletePersonalQueue(queueId)
      if (success) {
        await loadPersonalQueues() // Refresh the list
      }
    } catch (error) {
      console.error('Failed to delete queue:', error)
    }
  }

  const handlePrivacyUpdate = async () => {
    // Refresh the list when privacy is updated
    await loadPersonalQueues()
  }

  const handleQueueUpdate = (updatedQueue: PersonalQueue) => {
    // Update the queue in the local state
    setPersonalQueues(queues =>
      queues.map(queue =>
        queue.id === updatedQueue.id ? updatedQueue : queue
      )
    )
  }

  const handleBulkActionComplete = async () => {
    // Refresh the queue list after bulk actions
    await loadPersonalQueues()
    setSelectedQueues([])
  }

  const toggleBulkActions = () => {
    setShowBulkActions(!showBulkActions)
    if (showBulkActions) {
      setSelectedQueues([])
    }
  }

  const handleQueueSelectionChange = (queue: PersonalQueue, selected: boolean) => {
    if (selected) {
      setSelectedQueues(prev => [...prev, queue])
    } else {
      setSelectedQueues(prev => prev.filter(q => q.id !== queue.id))
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="space-y-6">
        <div className="glassmorphism rounded-2xl p-6">
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4 6h-3V5h-2v3H8v2h3v3h2v-3h3V8z"/>
              </svg>
            </div>
            <p className="text-dark-300 mb-2">Sign in required</p>
            <p className="text-sm text-dark-400">Sign in to create and manage your personal queues</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">
              My Queues
            </h1>
            <p className="text-dark-300">
              Create, manage, and share your personal video queues
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {personalQueues.length > 0 && (
              <button
                onClick={toggleBulkActions}
                className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                  showBulkActions
                    ? 'bg-primary-600 text-white'
                    : 'bg-dark-700 text-dark-300 hover:bg-dark-600 hover:text-white'
                }`}
              >
                {showBulkActions ? 'Cancel Selection' : 'Bulk Actions'}
              </button>
            )}
            <div className="text-right">
              <p className="text-sm text-white font-medium">{personalQueues.length}</p>
              <p className="text-xs text-dark-400">Total Queues</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-white font-medium">
                {personalQueues.filter(q => q.isPublic).length}
              </p>
              <p className="text-xs text-dark-400">Public</p>
            </div>
          </div>
        </div>
      </div>



      {/* Bulk Actions */}
      {showBulkActions && personalQueues.length > 0 && (
        <QueueBulkActions
          selectedQueues={selectedQueues}
          onSelectionChange={setSelectedQueues}
          onBulkActionComplete={handleBulkActionComplete}
          allQueues={personalQueues}
        />
      )}

      {/* Queues List */}
      <div className="glassmorphism rounded-2xl overflow-hidden">
        {isLoading ? (
          <div className="p-6 text-center">
            <div className="loading-spinner w-8 h-8 mx-auto mb-3"></div>
            <p className="text-dark-300">Loading your queues...</p>
          </div>
        ) : personalQueues.length === 0 ? (
          <div className="p-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                <path d="M15 6H3v2h12V6zm0 4H3v2h12v-2zM3 16h8v-2H3v2zM17 6v8.18c-.31-.11-.65-.18-1-.18-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3V8h3V6h-5z"/>
              </svg>
            </div>
            <p className="text-dark-300 mb-2">No personal queues yet</p>
            <p className="text-sm text-dark-400">Create your first queue to get started</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
            {personalQueues.map((queue) => (
              <PersonalQueueCard
                key={queue.id}
                queue={queue}
                onLoad={handleLoadQueue}
                onDelete={handleDeleteQueue}
                onPrivacyUpdate={handlePrivacyUpdate}
                onUpdate={handleQueueUpdate}
                isSelectionMode={showBulkActions}
                isSelected={selectedQueues.some(q => q.id === queue.id)}
                onSelectionChange={handleQueueSelectionChange}
              />
            ))}
          </div>
        )}
      </div>

      {/* Queue Statistics */}
      {personalQueues.length > 0 && !showBulkActions && (
        <QueueStats queues={personalQueues} />
      )}
    </div>
  )
}
