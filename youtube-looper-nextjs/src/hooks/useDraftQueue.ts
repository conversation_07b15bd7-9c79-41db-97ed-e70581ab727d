'use client'

import { createContext, useContext } from 'react'
import { VideoMetadata, DraftVideoItem } from '@/lib/types/video'

export interface DraftQueueContextType {
  // Draft queue state
  draftItems: DraftVideoItem[]

  // Draft queue management
  addToDraft: (video: VideoMetadata, loopCount?: number, startTime?: number, endTime?: number) => string
  removeFromDraft: (draftId: string) => boolean
  updateDraftItem: (draftId: string, updates: Partial<Pick<DraftVideoItem, 'loopCount' | 'startTime' | 'endTime'>>) => boolean
  clearDraft: () => void
  isInDraft: (videoId: string) => boolean
  getDraftItem: (draftId: string) => DraftVideoItem | undefined

  // Draft queue statistics
  draftCount: number
  draftDuration: number

  // Creation mode
  isCreationMode: boolean
  enterCreationMode: () => void
  exitCreationMode: () => void

  // Edit mode
  isEditMode: boolean
  editingQueueId: string | null
  enterEditMode: (queue: PersonalQueue) => void
  exitEditMode: () => void

  // Queue creation from draft
  saveDraftAsQueue: (title: string, isPublic?: boolean) => Promise<string | null>
  updateExistingQueue: (title: string, description?: string, tags?: string[]) => Promise<boolean>
}

export const DraftQueueContext = createContext<DraftQueueContextType>({
  draftItems: [],

  addToDraft: () => '',
  removeFromDraft: () => false,
  updateDraftItem: () => false,
  clearDraft: () => {},
  isInDraft: () => false,
  getDraftItem: () => undefined,

  draftCount: 0,
  draftDuration: 0,

  isCreationMode: false,
  enterCreationMode: () => {},
  exitCreationMode: () => {},

  isEditMode: false,
  editingQueueId: null,
  enterEditMode: () => {},
  exitEditMode: () => {},

  saveDraftAsQueue: async () => null,
  updateExistingQueue: async () => false,
})

export const useDraftQueue = () => {
  const context = useContext(DraftQueueContext)
  if (!context) {
    throw new Error('useDraftQueue must be used within a DraftQueueProvider')
  }
  return context
}
