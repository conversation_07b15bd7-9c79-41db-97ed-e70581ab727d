{"name": "youtube-looper-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export", "firebase:emulators": "firebase emulators:start", "firebase:deploy": "npm run export && firebase deploy", "firebase:deploy:hosting": "npm run export && firebase deploy --only hosting", "firebase:deploy:firestore": "firebase deploy --only firestore"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "firebase": "^10.7.1", "next": "15.4.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5"}, "devDependencies": {"autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "15.4.4", "firebase-tools": "^14.11.1", "postcss": "^8", "tailwindcss": "^3.3.0"}}