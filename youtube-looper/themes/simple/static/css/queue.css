/* YouTube Looper - Queue Styles */

/* Queue Items */
.queue-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border: 1px solid rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.queue-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(139, 92, 246, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.queue-item:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(102, 126, 234, 0.1);
}

.queue-item:hover::before {
  opacity: 1;
}

.queue-item.active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.25), rgba(139, 92, 246, 0.15));
  border-color: rgba(102, 126, 234, 0.6);
  box-shadow:
    0 16px 40px rgba(102, 126, 234, 0.25),
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.queue-item.active::before {
  opacity: 0.7;
}

.queue-item-thumbnail {
  position: relative;
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.queue-item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 6px;
}

.queue-item:hover .queue-item-thumbnail img {
  transform: scale(1.08);
  filter: brightness(1.1) saturate(1.2);
}

.queue-item.active .queue-item-thumbnail img {
  transform: scale(1.05);
  filter: brightness(1.15) saturate(1.3);
}

.queue-item-number {
  position: absolute;
  top: 6px;
  left: 6px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7));
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 6px;
  line-height: 1;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.playing-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-size: 1.1rem;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: playingPulse 2s infinite;
  box-shadow:
    0 4px 16px rgba(16, 185, 129, 0.4),
    0 0 0 4px rgba(16, 185, 129, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.9);
}

@keyframes playingPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    box-shadow:
      0 4px 16px rgba(16, 185, 129, 0.4),
      0 0 0 4px rgba(16, 185, 129, 0.2);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.15);
    box-shadow:
      0 6px 20px rgba(16, 185, 129, 0.6),
      0 0 0 8px rgba(16, 185, 129, 0.3);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    box-shadow:
      0 4px 16px rgba(16, 185, 129, 0.4),
      0 0 0 4px rgba(16, 185, 129, 0.2);
  }
}

.queue-item-info {
  flex: 1;
  min-width: 0;
  padding-right: 0.5rem;
}

.queue-item-title {
  font-weight: 600;
  color: white;
  font-size: 0.95rem;
  line-height: 1.4;
  margin-bottom: 0.375rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.queue-item.active .queue-item-title {
  color: #f8fafc;
  font-weight: 700;
}

.queue-item:hover .queue-item-title {
  color: #f1f5f9;
}

.queue-item-meta {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.queue-item.active .queue-item-meta {
  color: rgba(255, 255, 255, 0.9);
}

/* Queue Item Actions */
.queue-item-actions {
  display: flex;
  gap: 0.375rem;
  align-items: center;
  flex-shrink: 0;
}

.queue-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.9rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));
  color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.queue-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.queue-btn:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.15));
  color: white;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.25);
}

.queue-btn:hover::before {
  opacity: 1;
}

.queue-btn.play-btn:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.4), rgba(16, 185, 129, 0.3));
  color: #10b981;
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
}

.queue-btn.copy-url-btn:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(59, 130, 246, 0.3));
  color: #3b82f6;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}



/* Queue Cards */
.queue-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0.75rem;
  gap: 1rem;
}

.queue-card:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

.queue-thumbnail {
  position: relative;
  width: 80px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 8px;
  flex-shrink: 0;
}

.queue-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-thumbnail {
  font-size: 2rem;
  color: rgba(255, 255, 255, 0.8);
}

.queue-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}

.queue-thumbnail:hover .queue-overlay {
  opacity: 1;
}

.play-queue-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #1e293b;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  font-size: 0.75rem;
}

.play-queue-btn:hover {
  transform: scale(1.1);
  background: white;
}

.queue-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.queue-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: white;
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.queue-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.4rem;
  margin: 0;
}

.queue-meta span {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.15rem 0.35rem;
  border-radius: 4px;
}

.privacy-badge {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.privacy-badge.public {
  background: rgba(34, 197, 94, 0.2) !important;
  color: #86efac !important;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.privacy-badge.private {
  background: rgba(156, 163, 175, 0.2) !important;
  color: #d1d5db !important;
  border: 1px solid rgba(156, 163, 175, 0.3);
}

/* Queue Privacy Controls in Personal List */
.queue-privacy-controls {
  margin-top: 0.4rem;
  padding-top: 0.4rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.queue-date {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.queue-actions {
  display: flex;
  gap: 0.4rem;
  flex-shrink: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.queue-card:hover .queue-actions {
  opacity: 1;
}

.queue-action-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  font-size: 0.8rem;
}

.delete-btn {
  background: rgba(239, 68, 68, 0.8);
  color: white;
}

.delete-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

/* Draft Queue Styles */
.draft-queue-container {
  background: linear-gradient(135deg, #0f172a, #1e293b);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.draft-queue-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.draft-queue-header h4 {
  margin: 0;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.draft-queue-list {
  max-height: 300px;
  overflow-y: auto;
}

.draft-queue-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.draft-queue-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.draft-queue-item:last-child {
  margin-bottom: 0;
}

.draft-item-thumbnail {
  position: relative;
  flex-shrink: 0;
}

.draft-item-thumbnail img {
  width: 60px;
  height: 45px;
  border-radius: 8px;
  object-fit: cover;
}

.draft-item-info {
  flex: 1;
  min-width: 0;
}

.draft-item-title {
  color: white;
  font-weight: 500;
  font-size: 0.95rem;
  line-height: 1.3;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.draft-item-meta {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

.draft-item-actions {
  display: flex;
  gap: 0.5rem;
}

.draft-item-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.draft-item-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.draft-item-btn.remove-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

/* Queue Browser Specific Styles */
.browser-queue .queue-info {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.queue-card-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: white;
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.queue-card-stats {
  display: flex;
  gap: 0.75rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

.queue-card-meta {
  display: flex;
  gap: 0.75rem;
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.5);
}

.queue-card-actions {
  display: flex;
  gap: 0.4rem;
  flex-shrink: 0;
}

.queue-load-btn,
.queue-preview-btn {
  padding: 0.4rem 0.6rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.queue-load-btn:hover,
.queue-preview-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Hide the old preview grid for browser queues in horizontal layout */
.browser-queue .queue-card-preview {
  display: none;
}
