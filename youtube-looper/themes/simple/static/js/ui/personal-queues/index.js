// ============================================================================
// PERSONAL QUEUES CONTROLLER - MAIN INDEX
// ============================================================================

/**
 * Personal Queues Controller Index
 * Coordinates all personal queue components and provides unified API
 * This file serves as the main entry point for personal queue functionality
 */

/**
 * Initialize all personal queue components
 */
function initializePersonalQueues() {
  console.log('👤 Initializing Personal Queues Controller...');

  // Initialize all components
  initializePersonalQueueDisplay();
  initializePersonalQueuePrivacy();
  initializePersonalQueueLoading();
  initializePersonalQueueActions();
  initializePersonalQueueEventsComponent();
  initializePersonalQueueUtils();

  // Set up global event listeners
  setupGlobalPersonalQueueEvents();

  // Initial state update
  updateAllPersonalQueueComponents();

  console.log('✅ Personal Queues Controller initialized');
}

/**
 * Initialize personal queue display component
 */
function initializePersonalQueueDisplay() {
  // Display component is initialized when it loads
  console.log('📺 Personal queue display component ready');
}

/**
 * Initialize personal queue privacy component
 */
function initializePersonalQueuePrivacy() {
  if (typeof initializeQueuePrivacyManagement === 'function') {
    initializeQueuePrivacyManagement();
  }
  console.log('🔒 Personal queue privacy component ready');
}

/**
 * Initialize personal queue loading component
 */
function initializePersonalQueueLoading() {
  // Loading component is initialized when it loads
  console.log('📂 Personal queue loading component ready');
}

/**
 * Initialize personal queue actions component
 */
function initializePersonalQueueActions() {
  // Actions component is initialized when it loads
  console.log('⚡ Personal queue actions component ready');
}

/**
 * Initialize personal queue events component
 */
function initializePersonalQueueEventsComponent() {
  if (typeof initializePersonalQueueEvents === 'function') {
    initializePersonalQueueEvents();
  }
  console.log('🎯 Personal queue events component ready');
}

/**
 * Initialize personal queue utils component
 */
function initializePersonalQueueUtils() {
  // Utils component is initialized when it loads
  console.log('🛠️ Personal queue utils component ready');
}

/**
 * Set up global event listeners for personal queues
 */
function setupGlobalPersonalQueueEvents() {
  // Listen for authentication state changes
  document.addEventListener('authStateChanged', handleAuthStateChange);
  
  // Listen for view changes
  document.addEventListener('viewChanged', handleViewChange);
  
  // Listen for queue updates
  document.addEventListener('personalQueueUpdated', handlePersonalQueueUpdate);
  
  console.log('🔗 Global personal queue events set up');
}

/**
 * Handle authentication state changes
 * @param {CustomEvent} event - Auth state change event
 */
function handleAuthStateChange(event) {
  const { isAuthenticated, userId } = event.detail || {};
  
  console.log('🔐 Auth state changed:', isAuthenticated, userId);
  
  if (isAuthenticated) {
    // User signed in - load their queues if on personal view
    if (getCurrentView() === 'personal') {
      if (typeof loadPersonalQueues === 'function') {
        loadPersonalQueues(true);
      }
    }
  } else {
    // User signed out - clear cache and show sign-in required
    if (typeof invalidatePersonalQueuesCache === 'function') {
      invalidatePersonalQueuesCache();
    }
    
    const container = document.getElementById('personal-queues-list');
    if (container && typeof showSignInRequiredState === 'function') {
      showSignInRequiredState(container);
    }
  }
}

/**
 * Handle view changes
 * @param {CustomEvent} event - View change event
 */
function handleViewChange(event) {
  const { newView, oldView } = event.detail || {};
  
  console.log('📱 View changed:', oldView, '->', newView);
  
  if (newView === 'personal') {
    // Switched to personal view - load queues
    if (typeof loadPersonalQueues === 'function') {
      loadPersonalQueues();
    }
  }
}

/**
 * Handle personal queue updates
 * @param {CustomEvent} event - Queue update event
 */
function handlePersonalQueueUpdate(event) {
  const { queueId, action, data } = event.detail || {};
  
  console.log('🔄 Personal queue updated:', queueId, action, data);
  
  // Invalidate cache
  if (typeof invalidatePersonalQueuesCache === 'function') {
    invalidatePersonalQueuesCache();
  }
  
  // Refresh display if on personal view
  if (getCurrentView() === 'personal') {
    if (typeof refreshPersonalQueuesDisplay === 'function') {
      refreshPersonalQueuesDisplay();
    }
  }
}

/**
 * Update all personal queue components
 */
function updateAllPersonalQueueComponents() {
  // Update display if on personal view
  if (getCurrentView() === 'personal') {
    if (typeof loadPersonalQueues === 'function') {
      loadPersonalQueues();
    }
  }
}

/**
 * Refresh entire personal queues interface
 */
function refreshPersonalQueuesInterface() {
  console.log('🔄 Refreshing personal queues interface...');
  
  // Invalidate cache
  if (typeof invalidatePersonalQueuesCache === 'function') {
    invalidatePersonalQueuesCache();
  }
  
  // Reload queues
  if (typeof loadPersonalQueues === 'function') {
    loadPersonalQueues(true);
  }
  
  console.log('✅ Personal queues interface refreshed');
}

/**
 * Get personal queues state
 * @returns {Object} Current personal queues state
 */
function getPersonalQueuesState() {
  const cacheStats = typeof getCacheStats === 'function' ? getCacheStats() : {};
  
  return {
    isAuthenticated: typeof isUserAuthenticated === 'function' ? isUserAuthenticated() : false,
    currentView: getCurrentView(),
    cache: cacheStats,
    isOnPersonalView: getCurrentView() === 'personal'
  };
}

/**
 * Emit personal queue state change event
 * @param {string} type - Type of change
 * @param {*} data - Additional data
 */
function emitPersonalQueueStateChange(type, data = null) {
  const event = new CustomEvent('personalQueueStateChanged', {
    detail: { type, data, state: getPersonalQueuesState() }
  });
  
  document.dispatchEvent(event);
}

/**
 * Personal Queues API
 * Provides a unified interface for all personal queue operations
 */
const PersonalQueuesAPI = {
  // State
  getState: getPersonalQueuesState,
  refresh: refreshPersonalQueuesInterface,
  
  // Loading
  load: (forceRefresh) => typeof loadPersonalQueues === 'function' && loadPersonalQueues(forceRefresh),
  loadQueue: (queueId) => typeof loadPersonalQueue === 'function' && loadPersonalQueue(queueId),
  
  // Actions
  delete: (queueId) => typeof deletePersonalQueue === 'function' && deletePersonalQueue(queueId),
  duplicate: (queueId) => typeof duplicatePersonalQueue === 'function' && duplicatePersonalQueue(queueId),
  rename: (queueId) => typeof renamePersonalQueue === 'function' && renamePersonalQueue(queueId),
  export: (queueId) => typeof exportPersonalQueue === 'function' && exportPersonalQueue(queueId),
  
  // Privacy
  togglePrivacy: (queueId) => typeof toggleQueuePrivacy === 'function' && toggleQueuePrivacy(queueId),
  updatePrivacy: (queueId, isPublic) => typeof updateQueuePrivacy === 'function' && updateQueuePrivacy(queueId, isPublic),
  
  // Cache
  invalidateCache: () => typeof invalidatePersonalQueuesCache === 'function' && invalidatePersonalQueuesCache(),
  getCacheStats: () => typeof getCacheStats === 'function' && getCacheStats(),
  
  // Utils
  formatTime: (date) => typeof formatRelativeTime === 'function' && formatRelativeTime(date),
  formatDuration: (seconds) => typeof formatDuration === 'function' && formatDuration(seconds),
  validateTitle: (title) => typeof validateQueueTitle === 'function' && validateQueueTitle(title),
  
  // Events
  emit: emitPersonalQueueStateChange
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializePersonalQueues);
} else {
  // Small delay to ensure all components are loaded
  setTimeout(initializePersonalQueues, 50);
}

// Export API for global access
window.PersonalQueues = PersonalQueuesAPI;
window.initializePersonalQueues = initializePersonalQueues;
window.refreshPersonalQueuesInterface = refreshPersonalQueuesInterface;
window.getPersonalQueuesState = getPersonalQueuesState;
window.emitPersonalQueueStateChange = emitPersonalQueueStateChange;

console.log('✅ Personal Queues Controller Index loaded');
